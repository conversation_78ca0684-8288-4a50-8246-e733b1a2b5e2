ANTHROPIC_API_KEY=************************************************************************************************************

BACKSAPCE=************************************************************************************************************

DAYTONA_API_KEY=dtn_6c37b5888e486eb9e12b3c177fe57970e495f2790469a04e5167c67780b38bd5

# To separate your traces from other application
LANGSMITH_TRACING=true
LANGSMITH_ENDPOINT="https://api.smith.langchain.com"
LANGSMITH_API_KEY="***************************************************"
LANGSMITH_PROJECT="backspace-testing"


GITHUB_APP_ID=1341150
GITHUB_INSTALLATION_ID=68917691
GITHUB_PRIVATE_KEY="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"


BASE_IMAGE=backspaceinc/base-sandbox:latest
DOCKER_ORG=backspaceinc

SUPABASE_URI=https://tnkgfginqwcrtplppxfz.supabase.co
SUPABASE_API_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRua2dmZ2lucXdjcnRwbHBweGZ6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0NDA1OTgsImV4cCI6MjA2NDAxNjU5OH0.7sFrsdAHvuCfdcFPDRdPhugoQc7pnDDuLk8EbKszeO8
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRua2dmZ2lucXdjcnRwbHBweGZ6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0ODQ0MDU5OCwiZXhwIjoyMDY0MDE2NTk4fQ.Hb9kGAFcXOg4RP3QcpbepmakSU7H2roBi7X03hzUAMU

GH_TOKEN=****************************************
GITHUB_WEBHOOK_SECRET=backspace_webhook_secret_2024_secure_gh_integration

DEFAULT_REPO_ID=990171565



E2B_API_KEY=e2b_e25998ec95f66735566bdaa1b41e82a09fc5fb9f