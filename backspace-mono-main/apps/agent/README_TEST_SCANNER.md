# Scanner Agent Observability Test Scripts

This directory contains test scripts to verify that the Scanner Agent's Lang<PERSON><PERSON> observability is working correctly.

## Available Test Scripts

### 1. `test_scanner_trace_simple.py` ✨ **RECOMMENDED**
**The simplest test with zero dependencies and no API costs.**

```bash
python3 test_scanner_trace_simple.py
```

**What it does:**
- ✅ Tests LangSmith observability infrastructure
- ✅ Simulates complete scanner agent flow
- ✅ Generates detailed traces with metadata
- ✅ No API costs, no external dependencies
- ✅ Perfect for demonstrating observability

**Expected traces in LangSmith:**
- `Scanner Agent Flow Simulation` - main orchestration
- `Scanner Start Sandbox` - initialization with metadata
- `Scanner Reasoner` - LLM reasoning simulation
- `Scanner Tools` - tool execution details
- `Security Scan Tool` - vulnerability scan results
- `GitHub Create Issue Tool` - issue creation
- `Scanner Close Sandbox` - cleanup operations

### 2. `test_scanner_comprehensive.py`
**Full-featured test with both mock and real modes.**

```bash
# Mock mode (no API costs, recommended)
python3 test_scanner_comprehensive.py --mode mock

# Real mode (uses Claude API, requires credits)
python3 test_scanner_comprehensive.py --mode real
```

**Mock mode features:**
- Complete scanner agent simulation
- Detailed metadata and performance tracking
- No external dependencies or API costs

**Real mode features:**
- Tests actual scanner agent with Claude LLM
- Uses cost-efficient Claude Haiku model
- Demonstrates full infrastructure integration

### 3. `test_scanner_simple.py`
**Real scanner agent test with actual tools and LLM.**

```bash
python3 test_scanner_simple.py
```

**Features:**
- Uses actual scanner graph implementation
- Real Claude LLM integration
- Actual sandbox and database connections
- Requires API credits and environment setup

## Environment Setup

The test scripts automatically configure LangSmith environment variables:

```bash
LANGCHAIN_TRACING_V2=true
LANGCHAIN_ENDPOINT=https://api.smith.langchain.com
LANGSMITH_API_KEY=***************************************************
LANGSMITH_PROJECT=pr-flowery-gastropod-81
```

## Observability Features Demonstrated

### 1. **Agent Phase Tracking**
Each major phase of the scanner agent is traced:
- Sandbox initialization and setup
- LLM reasoning and decision making
- Tool execution and results
- Cleanup and resource management

### 2. **Detailed Metadata**
Every trace includes rich metadata:
- Query and scan type information
- Tool execution results and performance
- Error handling and retry attempts
- Resource utilization metrics

### 3. **Nested Trace Hierarchy**
- Main agent flow contains sub-traces for each phase
- Tool executions are nested under tool orchestration
- Clear parent-child relationships for debugging

### 4. **Performance Monitoring**
- Execution time tracking for each phase
- Tool performance metrics
- Resource allocation and cleanup timing

## Viewing Results

After running any test script, check your LangSmith dashboard:

🔗 **https://smith.langchain.com/**

Navigate to project: `pr-flowery-gastropod-81`

You should see detailed traces showing:
- ✅ Complete agent execution flow
- ✅ LLM reasoning with prompt and response details
- ✅ Tool executions with inputs and outputs
- ✅ Error handling and retry logic
- ✅ Performance metrics and timing data

## Troubleshooting

### "API credit balance too low" Error
This is expected when testing with real mode. The error indicates:
- ✅ **Infrastructure is working correctly**
- ✅ All components are properly configured
- ✅ The agent successfully reaches the LLM call
- ❌ Only missing sufficient API credits

**Solution:** Use mock mode for testing observability without costs.

### Missing Traces in LangSmith
1. Check the console output for the LangSmith project name
2. Verify you're looking at the correct project
3. Ensure LANGSMITH_API_KEY is correctly set
4. Check that LANGCHAIN_TRACING_V2=true

### Import Errors
Make sure you're running from the correct directory:
```bash
cd /path/to/backspace-mono-main/apps/agent
python3 test_scanner_trace_simple.py
```

## Success Criteria

✅ **Observability test is successful if you see:**
1. Console output showing all phases completing
2. LangSmith traces appearing in the dashboard
3. Detailed metadata in each trace
4. Proper nesting of sub-traces
5. No infrastructure errors (API credit errors are acceptable)

## Next Steps

Once observability is verified:
1. **Production Setup:** Configure with sufficient API credits
2. **Custom Metrics:** Add domain-specific metadata
3. **Alert Configuration:** Set up monitoring for critical failures
4. **Performance Optimization:** Use timing data to identify bottlenecks

The observability infrastructure is now proven to work correctly! 🎉