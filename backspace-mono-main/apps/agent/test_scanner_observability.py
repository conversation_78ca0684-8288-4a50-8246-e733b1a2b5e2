#!/usr/bin/env python3
"""
Test script to verify Scanner Agent observability with mock LLM (no API credits needed).
This creates a test that generates proper LangSmith traces without requiring API calls.
"""

import asyncio
import os
import sys
import logging
from pathlib import Path
from typing import Optional, List, Any

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_environment():
    """Set up required environment variables for the scanner agent."""
    
    # LangSmith configuration
    os.environ["LANGCHAIN_TRACING_V2"] = "true"
    os.environ["LANGCHAIN_ENDPOINT"] = "https://api.smith.langchain.com"
    
    # Use existing API key from .env or set default
    if not os.getenv("LANGSMITH_API_KEY"):
        os.environ["LANGSMITH_API_KEY"] = "***************************************************"
    
    if not os.getenv("LANGSMITH_PROJECT"):
        os.environ["LANGSMITH_PROJECT"] = "pr-flowery-gastropod-81"
    
    # Mock API keys (not actually used with FakeChatModel)
    os.environ["ANTHROPIC_API_KEY"] = "fake-key-for-testing"
    
    # Daytona API key (for sandbox management) - comment out to skip sandbox
    # os.environ["DAYTONA_API_KEY"] = "dtn_6c37b5888e486eb9e12b3c177fe57970e495f2790469a04e5167c67780b38bd5"
    
    # GitHub configuration
    if not os.getenv("GH_TOKEN"):
        os.environ["GH_TOKEN"] = "****************************************"
    
    # Supabase configuration - comment out to skip database
    # os.environ["SUPABASE_URI"] = "https://tnkgfginqwcrtplppxfz.supabase.co"
    # os.environ["SUPABASE_API_KEY"] = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRua2dmZ2lucXdjcnRwbHBweGZ6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0NDA1OTgsImV4cCI6MjA2NDAxNjU5OH0.7sFrsdAHvuCfdcFPDRdPhugoQc7pnDDuLk8EbKszeO8"
    
    # Default repository ID
    if not os.getenv("DEFAULT_REPO_ID"):
        os.environ["DEFAULT_REPO_ID"] = "990171565"
    
    logger.info("✅ Environment variables configured")
    logger.info(f"📊 LangSmith Project: {os.getenv('LANGSMITH_PROJECT')}")
    logger.info(f"🔑 LangSmith API Key: {os.getenv('LANGSMITH_API_KEY')[:20]}...")

class MockLLM:
    """Mock LLM that simulates Claude responses without API calls."""
    
    def __init__(self):
        self.call_count = 0
    
    async def ainvoke(self, messages, **kwargs):
        """Mock LLM response that simulates tool usage."""
        self.call_count += 1
        
        # Import here to avoid circular imports
        from langchain_core.messages import AIMessage, ToolCall
        
        # Simulate different responses based on call count
        if self.call_count == 1:
            # First call: agent decides to use tools
            return AIMessage(
                content="I'll analyze the codebase for security vulnerabilities and create an issue if needed.",
                tool_calls=[
                    ToolCall(
                        name="run_security_scan",
                        args={"directory": "/home/<USER>/workspace", "scan_type": "comprehensive"},
                        id="call_security_scan_1"
                    )
                ]
            )
        else:
            # Second call: summarize results
            return AIMessage(
                content="Security scan completed. Found 2 medium severity issues and created GitHub issue #42 to track them.",
                tool_calls=[]  # No more tool calls, will end the conversation
            )

class MockTool:
    """Mock tool that simulates tool execution."""
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
    
    async def ainvoke(self, input_data, **kwargs):
        """Mock tool execution."""
        from langchain_core.messages import ToolMessage
        
        if self.name == "run_security_scan":
            return ToolMessage(
                content="Security scan completed. Found 2 medium severity vulnerabilities: hardcoded secrets in config.py and SQL injection risk in user_handler.py",
                tool_call_id="call_security_scan_1"
            )
        elif self.name == "github_create_issue":
            return ToolMessage(
                content="Created GitHub issue #42: 'Security vulnerabilities found during automated scan'",
                tool_call_id="call_github_create_1"
            )
        else:
            return ToolMessage(
                content=f"Mock execution of {self.name} completed successfully",
                tool_call_id=f"call_{self.name}_1"
            )

async def create_mock_scanner_graph():
    """Create a mock scanner graph for testing observability without API calls."""
    try:
        # Import the scanner graph class
        from agents.scanner.graph import ScannerGraph
        from agents.scanner.states import ScanType
        from langsmith import traceable
        
        # Create mock LLM and tools
        mock_llm = MockLLM()
        mock_tools = [
            MockTool("run_security_scan", "Scan codebase for security vulnerabilities"),
            MockTool("github_create_issue", "Create a GitHub issue"),
            MockTool("sandbox_run_command", "Run command in sandbox"),
        ]
        
        logger.info("✅ Mock LLM and tools initialized")
        logger.info(f"✅ {len(mock_tools)} mock tools configured: {[t.name for t in mock_tools]}")
        
        # Create a modified scanner graph class for testing
        class MockScannerGraph(ScannerGraph):
            def __init__(self, llm, tools):
                # Initialize with mock components
                self.llm = llm
                self.tools = tools
                # Set up retry policy
                self.retry_policy = None
        
        # Create mock scanner graph
        scanner_graph = MockScannerGraph(llm=mock_llm, tools=mock_tools)
        compiled_graph = scanner_graph.compile()
        
        logger.info("✅ Mock scanner graph compiled successfully")
        return compiled_graph
        
    except Exception as e:
        logger.error(f"❌ Failed to create mock scanner graph: {e}")
        import traceback
        traceback.print_exc()
        raise

async def run_mock_scanner_test(graph, test_query: str = "Analyze the code quality and create a GitHub issue if any problems are found", scan_type: Optional[str] = "security"):
    """Run a mock scanner test with the given query."""
    try:
        from agents.scanner.states import GraphState, ScanType
        from langsmith import traceable, get_current_run_tree
        
        # Wrap the test in a traceable function
        @traceable(
            name="Scanner Test Run",
            run_type="chain",
            metadata={"test": True, "mock": True},
            tags=["scanner-test", "mock", "observability"]
        )
        async def execute_test():
            # Convert string scan_type to enum if provided
            scan_type_enum = None
            if scan_type:
                try:
                    scan_type_enum = ScanType(scan_type)
                except ValueError:
                    logger.warning(f"Invalid scan_type '{scan_type}', using comprehensive scan")
                    scan_type_enum = ScanType.COMPREHENSIVE
            
            # Create initial state
            initial_state: GraphState = {
                "messages": [],
                "query": test_query,
                "sandbox_session_id": None,  # Skip sandbox for mock test
                "repo_id": os.getenv("DEFAULT_REPO_ID", "990171565"),
                "scan_results": "",
                "issues_created": 0,
                "metrics": None,
                "scan_type": scan_type_enum
            }
            
            logger.info("🎯 Starting mock scanner agent test...")
            logger.info(f"📝 Query: {test_query}")
            logger.info(f"🔍 Scan type: {scan_type}")
            logger.info(f"📁 Repository ID: {initial_state['repo_id']}")
            
            # Add metadata to current run
            current_run = get_current_run_tree()
            if current_run:
                current_run.add_metadata({
                    "query": test_query,
                    "scan_type": scan_type,
                    "repo_id": initial_state['repo_id'],
                    "mock_test": True
                })
            
            # Configure with tracing
            config = {
                "configurable": {"thread_id": "mock-scanner-session"},
                "metadata": {
                    "test_run": True,
                    "mock_run": True,
                    "scan_type": scan_type,
                    "query": test_query[:100]
                },
                "tags": ["scanner-test", "mock", "observability", f"scan-{scan_type}"]
            }
            
            # Run the graph
            result = await graph.ainvoke(initial_state, config=config)
            
            logger.info("✅ Mock scanner test completed successfully")
            logger.info(f"📊 Issues created: {result.get('issues_created', 0)}")
            logger.info(f"📄 Scan results length: {len(result.get('scan_results', ''))}")
            
            return result
        
        return await execute_test()
        
    except Exception as e:
        logger.error(f"❌ Mock scanner test failed: {e}")
        import traceback
        traceback.print_exc()
        raise

async def main():
    """Main test function."""
    print("=" * 80)
    print("🎯 Scanner Agent Observability Test (Mock Version)")
    print("=" * 80)
    
    try:
        # Setup environment
        setup_environment()
        
        # Create mock scanner graph
        print("\n🔧 Setting up mock scanner graph...")
        graph = await create_mock_scanner_graph()
        
        # Run mock test
        print("\n🚀 Running mock scanner test...")
        result = await run_mock_scanner_test(
            graph=graph,
            test_query="Review this codebase for security vulnerabilities and create an issue if critical problems are found",
            scan_type="security"
        )
        
        # Print success message
        print("\n" + "=" * 80)
        print("✅ Mock test completed successfully!")
        print("=" * 80)
        print(f"📊 LangSmith Project: {os.getenv('LANGSMITH_PROJECT')}")
        print("🔗 Check your traces at: https://smith.langchain.com/")
        print("\n📈 You should see traces for:")
        print("   ✅ Scanner Test Run - main test wrapper")
        print("   ✅ Scanner Start Sandbox - initialization (mocked)")
        print("   ✅ Scanner Reasoner - LLM reasoning (mocked)")
        print("   ✅ Scanner Tools - tool execution (mocked)")
        print("   ✅ Scanner Close Sandbox - cleanup (mocked)")
        print("\n🎉 Full observability demonstrated without API costs!")
        print("\n💡 This proves the tracing infrastructure works correctly.")
        print("   Replace MockLLM with real ChatAnthropic when ready to test with actual API.")
        
        return result
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.error(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    # Run the async main function
    result = asyncio.run(main())
    if result:
        sys.exit(0)
    else:
        sys.exit(1)