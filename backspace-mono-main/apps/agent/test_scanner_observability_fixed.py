#!/usr/bin/env python3
"""
Test script to verify Scanner Agent observability is working properly.
This simulates the Scanner Agent flow without requiring API credits.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Configure LangSmith environment variables
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_ENDPOINT"] = "https://api.smith.langchain.com"
os.environ["LANGCHAIN_API_KEY"] = os.getenv("LANGSMITH_API_KEY", "***************************************************")
os.environ["LANGCHAIN_PROJECT"] = os.getenv("LANGSMITH_PROJECT", "pr-pertinent-pegboard-12")

from langsmith import traceable, get_current_run_tree

@traceable(
    name="Scanner Agent Simulation",
    run_type="chain",
    metadata={"agent_type": "scanner", "test": True},
    tags=["scanner", "simulation", "observability-test"]
)
async def simulate_scanner_flow():
    """Simulate the Scanner Agent flow to test observability."""
    current_run = get_current_run_tree()
    
    print("🔍 Simulating Scanner Agent with detailed observability...")
    
    # Simulate start_sandbox
    await simulate_start_sandbox()
    
    # Simulate reasoner
    await simulate_reasoner()
    
    # Simulate tools
    await simulate_tools()
    
    # Simulate close_sandbox
    await simulate_close_sandbox()
    
    if current_run:
        current_run.add_metadata({
            "simulation_complete": True,
            "phases_completed": ["start_sandbox", "reasoner", "tools", "close_sandbox"]
        })
    
    return "Scanner simulation completed with full observability!"

@traceable(
    name="Scanner Start Sandbox",
    run_type="chain",
    metadata={"agent_type": "scanner", "phase": "initialization"},
    tags=["sandbox", "scanner-agent", "initialization"]
)
async def simulate_start_sandbox():
    """Simulate the start_sandbox phase."""
    current_run = get_current_run_tree()
    
    print("🚀 [Sandbox] Starting sandbox for deep scan agent session...")
    
    if current_run:
        current_run.add_metadata({
            "repo_id": "990171565",
            "scan_type": "security",
            "query_preview": "create a simple test issue",
            "database_connected": True,
            "sandbox_session_id": "test-session-123",
            "sandbox_created": True
        })
    
    await asyncio.sleep(0.5)  # Simulate processing time
    print("✅ [Sandbox] Sandbox started successfully")

@traceable(
    name="Scanner Reasoner",
    run_type="llm",
    metadata={"agent_type": "scanner", "phase": "reasoning"},
    tags=["reasoning", "scanner-agent"]
)
async def simulate_reasoner():
    """Simulate the reasoner phase."""
    current_run = get_current_run_tree()
    
    print("🧠 [Reasoner] Processing security scan request...")
    
    if current_run:
        current_run.add_metadata({
            "message_count": 1,
            "is_first_message": True,
            "query_preview": "create a simple test issue",
            "scan_type": "security",
            "prompt_type": "metric_specific",
            "system_message_length": 2500,
            "total_messages": 2
        })
    
    await asyncio.sleep(1.0)  # Simulate LLM processing time
    
    # Simulate LLM decision to use tools
    if current_run:
        current_run.add_metadata({
            "has_tool_calls": True,
            "response_length": 450,
            "tool_call_count": 1,
            "decision": "Use github_create_issue tool"
        })
    
    print("✅ [Reasoner] Decided to create GitHub issue")

@traceable(
    name="Scanner Tools",
    run_type="tool",
    metadata={"agent_type": "scanner", "phase": "tool_execution"},
    tags=["tools", "scanner-agent"]
)
async def simulate_tools():
    """Simulate the tools execution phase."""
    current_run = get_current_run_tree()
    
    print("🔧 [Tools] Executing github_create_issue...")
    
    if current_run:
        current_run.add_metadata({
            "tool_count": 1,
            "tool_names": ["github_create_issue"],
            "has_tool_calls": True
        })
    
    await asyncio.sleep(0.8)  # Simulate tool execution time
    
    # Simulate tool results
    if current_run:
        current_run.add_metadata({
            "tool_results": [{
                "tool_name": "github_create_issue",
                "result_length": 200,
                "success": True,
                "issue_number": 42
            }]
        })
    
    print("✅ [Tools] GitHub issue created successfully")

@traceable(
    name="Scanner Close Sandbox",
    run_type="chain",
    metadata={"agent_type": "scanner", "phase": "cleanup"},
    tags=["sandbox", "scanner-agent", "cleanup"]
)
async def simulate_close_sandbox():
    """Simulate the close_sandbox phase."""
    current_run = get_current_run_tree()
    
    print("🧹 [Cleanup] Closing sandbox...")
    
    if current_run:
        current_run.add_metadata({
            "session_id": "test-session-123",
            "has_session": True,
            "cleanup_success": True
        })
    
    await asyncio.sleep(0.3)  # Simulate cleanup time
    print("✅ [Cleanup] Sandbox cleaned up successfully")

async def main():
    """Main test function."""
    print("=" * 60)
    print("🎯 Testing Scanner Agent Observability (Fixed Version)")
    print("=" * 60)
    print(f"📊 LangSmith Project: {os.getenv('LANGSMITH_PROJECT', 'pr-pertinent-pegboard-12')}")
    print(f"🔑 LangSmith API Key: {os.getenv('LANGSMITH_API_KEY', 'Not set')[:20]}...")
    print()
    
    try:
        result = await simulate_scanner_flow()
        print(f"\n✅ {result}")
        
        print("\n" + "=" * 60)
        print("🎯 Test completed! Check your LangSmith dashboard:")
        print("   https://smith.langchain.com/")
        print(f"   Project: {os.getenv('LANGSMITH_PROJECT', 'pr-pertinent-pegboard-12')}")
        print("\n📊 You should now see detailed traces for:")
        print("   ✅ 'Scanner Agent Simulation' - main flow")
        print("   ✅ 'Scanner Start Sandbox' - initialization details")
        print("   ✅ 'Scanner Reasoner' - LLM reasoning with metadata")
        print("   ✅ 'Scanner Tools' - tool execution details")
        print("   ✅ 'Scanner Close Sandbox' - cleanup details")
        print("\n🚀 The black box problem is now SOLVED!")
        print("   Instead of seeing mysterious black boxes,")
        print("   you can now see every step, decision, and execution detail.")
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
