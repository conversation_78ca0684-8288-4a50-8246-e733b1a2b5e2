#!/usr/bin/env python3
"""
Test script to run the actual scanner agent and generate real traces.
This will create a GitHub issue using the real scanner agent flow.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from agents.scanner.agent import ScannerAgent
from agents.scanner.states import ScanType

async def test_real_scanner():
    """Test the real scanner agent with a simple, safe query."""
    
    print("🚀 Starting Real Scanner Agent Test")
    print("=" * 50)
    
    try:
        # Initialize the scanner agent with the same configuration as in the codebase
        print("📋 Initializing ScannerAgent...")
        scanner = ScannerAgent(
            model_provider="anthropic",
            model_name="claude-sonnet-4-20250514",
            use_sandbox=True
        )
        print("✅ ScannerAgent initialized successfully")
        
        # Use a simple, safe query that will trigger the reasoner->tools flow
        query = "create a github issue with title 'Hello from Scanner Agent' and body 'This is a test issue created by the scanner agent to demonstrate the reasoner->tools flow.'"
        
        print(f"\n🎯 Running query: {query}")
        print("-" * 50)
        
        # Run the scanner agent with the test query
        # This will generate real traces showing the reasoner->tools flow
        response = await scanner.run(query)
        
        print("\n" + "=" * 50)
        print("✅ Scanner Agent Test Completed Successfully!")
        print("🔍 Final Response:")
        print(response)
        
        print("\n📊 Trace Information:")
        print("- This execution generated real traces in LangSmith")
        print("- You can see the reasoner->tools flow in the trace")
        print("- The agent actually connected to GitHub and sandbox")
        print("- Compare this with your 'black box' screenshot to see the difference")
        
    except Exception as e:
        print(f"\n❌ Error running scanner agent: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Scanner Agent Real Test")
    print("This will run the actual scanner agent to generate real traces")
    print()
    
    # Run the test
    asyncio.run(test_real_scanner())