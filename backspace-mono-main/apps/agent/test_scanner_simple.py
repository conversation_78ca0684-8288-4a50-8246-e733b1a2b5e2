#!/usr/bin/env python3
"""
Simple test script to run the scanner agent and verify Lang<PERSON><PERSON> observability.
This creates a minimal test that generates proper traces in LangSmith.
"""

import asyncio
import os
import sys
import logging
from pathlib import Path
from typing import Optional

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_environment():
    """Set up required environment variables for the scanner agent."""
    
    # LangSmith configuration
    os.environ["LANGCHAIN_TRACING_V2"] = "true"
    os.environ["LANGCHAIN_ENDPOINT"] = "https://api.smith.langchain.com"
    
    # Use existing API key from .env or set default
    if not os.getenv("LANGSMITH_API_KEY"):
        os.environ["LANGSMITH_API_KEY"] = "***************************************************"
    
    if not os.getenv("LANGSMITH_PROJECT"):
        os.environ["LANGSMITH_PROJECT"] = "pr-flowery-gastropod-81"
    
    # Anthropic API key (required for LLM)
    if not os.getenv("ANTHROPIC_API_KEY"):
        os.environ["ANTHROPIC_API_KEY"] = "************************************************************************************************************"
    
    # Daytona API key (for sandbox management)
    if not os.getenv("DAYTONA_API_KEY"):
        os.environ["DAYTONA_API_KEY"] = "dtn_6c37b5888e486eb9e12b3c177fe57970e495f2790469a04e5167c67780b38bd5"
    
    # GitHub configuration (for tools that might need it)
    if not os.getenv("GH_TOKEN"):
        os.environ["GH_TOKEN"] = "****************************************"
    
    # Supabase configuration (optional, for database)
    if not os.getenv("SUPABASE_URI"):
        os.environ["SUPABASE_URI"] = "https://tnkgfginqwcrtplppxfz.supabase.co"
    if not os.getenv("SUPABASE_API_KEY"):
        os.environ["SUPABASE_API_KEY"] = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRua2dmZ2lucXdjcnRwbHBweGZ6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0NDA1OTgsImV4cCI6MjA2NDAxNjU5OH0.7sFrsdAHvuCfdcFPDRdPhugoQc7pnDDuLk8EbKszeO8"
    
    # Default repository ID
    if not os.getenv("DEFAULT_REPO_ID"):
        os.environ["DEFAULT_REPO_ID"] = "990171565"
    
    logger.info("✅ Environment variables configured")
    logger.info(f"📊 LangSmith Project: {os.getenv('LANGSMITH_PROJECT')}")
    logger.info(f"🔑 LangSmith API Key: {os.getenv('LANGSMITH_API_KEY')[:20]}...")

async def create_scanner_graph():
    """Create and configure the scanner graph with proper LLM and tools."""
    try:
        # Import after environment setup
        from langchain_anthropic import ChatAnthropic
        from agents.scanner.graph import ScannerGraph
        from agents.scanner.states import ScanType
        from agents.tools.sandbox_tools_state import read_tools
        from agents.tools.github_tools import github_tools
        from agents.tools.analysis_tools import analysis_tools
        
        # Initialize LLM
        llm = ChatAnthropic(
            model="claude-3-5-sonnet-20241022",
            temperature=0.1,
            max_tokens=4096,
        )
        logger.info("✅ Claude LLM initialized")
        
        # Combine essential tools for scanning
        tools = read_tools + github_tools[:2] + analysis_tools[:3]  # Limit tools to avoid complexity
        logger.info(f"✅ {len(tools)} tools configured: {[t.name for t in tools]}")
        
        # Create scanner graph
        scanner_graph = ScannerGraph(llm=llm, tools=tools)
        compiled_graph = scanner_graph.compile()
        
        logger.info("✅ Scanner graph compiled successfully")
        return compiled_graph
        
    except Exception as e:
        logger.error(f"❌ Failed to create scanner graph: {e}")
        raise

async def run_scanner_test(graph, test_query: str = "Analyze the code quality and create a GitHub issue if any problems are found", scan_type: Optional[str] = "security"):
    """Run a simple scanner test with the given query."""
    try:
        from agents.scanner.states import GraphState, ScanType
        
        # Convert string scan_type to enum if provided
        scan_type_enum = None
        if scan_type:
            try:
                scan_type_enum = ScanType(scan_type)
            except ValueError:
                logger.warning(f"Invalid scan_type '{scan_type}', using comprehensive scan")
                scan_type_enum = ScanType.COMPREHENSIVE
        
        # Create initial state
        initial_state: GraphState = {
            "messages": [],
            "query": test_query,
            "sandbox_session_id": None,
            "repo_id": os.getenv("DEFAULT_REPO_ID", "990171565"),
            "scan_results": "",
            "issues_created": 0,
            "metrics": None,
            "scan_type": scan_type_enum
        }
        
        logger.info("🎯 Starting scanner agent test...")
        logger.info(f"📝 Query: {test_query}")
        logger.info(f"🔍 Scan type: {scan_type}")
        logger.info(f"📁 Repository ID: {initial_state['repo_id']}")
        
        # Configure with tracing
        config = {
            "configurable": {"thread_id": "test-scanner-session"},
            "metadata": {
                "test_run": True,
                "scan_type": scan_type,
                "query": test_query[:100]
            },
            "tags": ["scanner-test", "observability", f"scan-{scan_type}"]
        }
        
        # Run the graph
        result = await graph.ainvoke(initial_state, config=config)
        
        logger.info("✅ Scanner test completed successfully")
        logger.info(f"📊 Issues created: {result.get('issues_created', 0)}")
        logger.info(f"📄 Scan results length: {len(result.get('scan_results', ''))}")
        
        return result
        
    except Exception as e:
        logger.error(f"❌ Scanner test failed: {e}")
        import traceback
        traceback.print_exc()
        raise

async def main():
    """Main test function."""
    print("=" * 80)
    print("🎯 Scanner Agent Observability Test")
    print("=" * 80)
    
    try:
        # Setup environment
        setup_environment()
        
        # Create scanner graph
        print("\n🔧 Setting up scanner graph...")
        graph = await create_scanner_graph()
        
        # Run simple test
        print("\n🚀 Running scanner test...")
        result = await run_scanner_test(
            graph=graph,
            test_query="Review this codebase for security vulnerabilities and create an issue if critical problems are found",
            scan_type="security"
        )
        
        # Print success message
        print("\n" + "=" * 80)
        print("✅ Test completed successfully!")
        print("=" * 80)
        print(f"📊 LangSmith Project: {os.getenv('LANGSMITH_PROJECT')}")
        print("🔗 Check your traces at: https://smith.langchain.com/")
        print("\n📈 You should see traces for:")
        print("   ✅ Scanner Start Sandbox - initialization with metadata")
        print("   ✅ Scanner Reasoner - LLM reasoning with tool decisions")
        print("   ✅ Scanner Tools - tool execution details")
        print("   ✅ Scanner Close Sandbox - cleanup operations")
        print("\n🎉 Full observability achieved!")
        
        return result
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        logger.error(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    # Run the async main function
    result = asyncio.run(main())
    if result:
        sys.exit(0)
    else:
        sys.exit(1)