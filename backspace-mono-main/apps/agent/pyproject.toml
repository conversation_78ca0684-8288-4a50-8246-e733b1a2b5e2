[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "backspace-agent"
version = "0.1.0"
description = "Backspace Agent"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "langgraph",
    "langchain",
    "langchain-core",
    "langchain-openai",
    "langgraph-cli[inmem]",
    "langgraph-api",
    "langchain-anthropic",
    "tavily-python",
    "python-dotenv",
    "docker>=7.1.0",
    "requests>=2.31.0",
    "supabase>=2.15.2",
    "langgraph-sdk>=0.1.70",
    "debugpy>=1.8.14",
    "fastapi>=0.110.0",
    "pygithub>=2.6.1",
    "daytona>=0.21.0",
    "e2b-code-interpreter>=1.5.1",
    "modal>=1.0.4",
    "claude-code-sdk",
    "ipykernel>=6.29.5",
]

[project.optional-dependencies]
dev = ["pytest", "black", "isort", "flake8", "ruff", "mypy"]

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-dir]
"" = "src"
