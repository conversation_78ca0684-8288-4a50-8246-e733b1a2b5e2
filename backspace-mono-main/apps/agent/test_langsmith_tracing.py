#!/usr/bin/env python3
"""
Test script to verify Lang<PERSON>mith tracing is working without API calls.
"""

import os
import sys
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Configure LangSmith environment variables
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_ENDPOINT"] = "https://api.smith.langchain.com"
os.environ["LANGCHAIN_API_KEY"] = os.getenv("LANGSMITH_API_KEY", "***************************************************")
os.environ["LANGCHAIN_PROJECT"] = os.getenv("LANGSMITH_PROJECT", "pr-pertinent-pegboard-12")

from langsmith import Client as LangSmithClient
from langsmith.run_helpers import traceable

# Initialize LangSmith client
langsmith_client = LangSmithClient(
    api_key=os.getenv("LANGSMITH_API_KEY"),
    api_url="https://api.smith.langchain.com"
)

@traceable(name="test_scanner_tracing")
def test_scanner_tracing():
    """Test function to verify LangSmith tracing."""
    print("🔍 Testing LangSmith tracing for Scanner Agent...")
    
    # Log a test run to LangSmith
    langsmith_client.create_run(
        name="test_scanner_tracing",
        run_type="chain",
        inputs={"test": "Scanner Agent LangSmith integration"},
        outputs={"result": "Tracing is working!"},
        project_name=os.getenv("LANGSMITH_PROJECT", "pr-pertinent-pegboard-12")
    )
    
    return "Tracing test completed!"

if __name__ == "__main__":
    print(f"📊 LangSmith Project: {os.getenv('LANGSMITH_PROJECT', 'pr-pertinent-pegboard-12')}")
    print(f"🔑 LangSmith API Key: {os.getenv('LANGSMITH_API_KEY', 'Not set')[:20]}...")
    
    result = test_scanner_tracing()
    print(f"✅ {result}")
    print("🔗 Check your LangSmith dashboard for traces: https://smith.langchain.com/")
