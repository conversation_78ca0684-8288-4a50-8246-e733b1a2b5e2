[I 2025-06-24 17:13:55.920 ServerApp] jupyter_lsp | extension was successfully linked.
[I 2025-06-24 17:13:55.991 ServerApp] jupyter_server_terminals | extension was successfully linked.
[I 2025-06-24 17:13:56.005 ServerApp] jupyterlab | extension was successfully linked.
[I 2025-06-24 17:13:56.018 ServerApp] notebook | extension was successfully linked.
[I 2025-06-24 17:13:58.145 ServerApp] notebook_shim | extension was successfully linked.
[I 2025-06-24 17:13:58.317 ServerApp] notebook_shim | extension was successfully loaded.
[I 2025-06-24 17:13:58.324 ServerApp] jupyter_lsp | extension was successfully loaded.
[I 2025-06-24 17:13:58.326 ServerApp] jupyter_server_terminals | extension was successfully loaded.
[I 2025-06-24 17:13:58.331 LabApp] JupyterLab extension loaded from /Users/<USER>/Downloads/backspace-mono-main/apps/agent/.venv/lib/python3.13/site-packages/jupyterlab
[I 2025-06-24 17:13:58.332 LabApp] JupyterLab application directory is /Users/<USER>/Downloads/backspace-mono-main/apps/agent/.venv/share/jupyter/lab
[I 2025-06-24 17:13:58.333 LabApp] Extension Manager is 'pypi'.
[I 2025-06-24 17:13:58.751 ServerApp] jupyterlab | extension was successfully loaded.
[I 2025-06-24 17:13:58.759 ServerApp] notebook | extension was successfully loaded.
[I 2025-06-24 17:13:58.761 ServerApp] Serving notebooks from local directory: /Users/<USER>/Downloads/backspace-mono-main/apps/agent
[I 2025-06-24 17:13:58.761 ServerApp] Jupyter Server 2.16.0 is running at:
[I 2025-06-24 17:13:58.761 ServerApp] http://localhost:8888/tree?token=de36ba55f1a4ad565e3f1571283e0f7b5b1842a17f20212c
[I 2025-06-24 17:13:58.761 ServerApp]     http://127.0.0.1:8888/tree?token=de36ba55f1a4ad565e3f1571283e0f7b5b1842a17f20212c
[I 2025-06-24 17:13:58.761 ServerApp] Use Control-C to stop this server and shut down all kernels (twice to skip confirmation).
[C 2025-06-24 17:13:58.766 ServerApp] 
    
    To access the server, open this file in a browser:
        file:///Users/<USER>/Library/Jupyter/runtime/jpserver-67168-open.html
    Or copy and paste one of these URLs:
        http://localhost:8888/tree?token=de36ba55f1a4ad565e3f1571283e0f7b5b1842a17f20212c
        http://127.0.0.1:8888/tree?token=de36ba55f1a4ad565e3f1571283e0f7b5b1842a17f20212c
[I 2025-06-24 17:13:59.234 ServerApp] Skipped non-installed server(s): bash-language-server, dockerfile-language-server-nodejs, javascript-typescript-langserver, jedi-language-server, julia-language-server, pyright, python-language-server, python-lsp-server, r-languageserver, sql-language-server, texlab, typescript-language-server, unified-language-server, vscode-css-languageserver-bin, vscode-html-languageserver-bin, vscode-json-languageserver-bin, yaml-language-server
[C 2025-06-24 17:18:32.482 ServerApp] received signal 15, stopping
[I 2025-06-24 17:18:32.490 ServerApp] Shutting down 5 extensions
