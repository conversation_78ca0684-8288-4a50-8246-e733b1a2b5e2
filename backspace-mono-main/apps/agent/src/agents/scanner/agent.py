"""
Scanner Agent - A comprehensive codebase security and quality analysis tool.
"""

import asyncio
import os
from typing import Optional, Literal
from langchain_core.messages import AIMessage, ToolMessage, HumanMessage
from agents.base import BaseAgent
from agents.scanner.graph import ScannerGraph
from agents.scanner.states import ScanType
from agents.tools import local_tools, read_tools
from agents.tools.github_tools import github_tools
from langchain_core.runnables import RunnableConfig

# Configure LangSmith environment variables
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_ENDPOINT"] = "https://api.smith.langchain.com"
os.environ["LANGCHAIN_API_KEY"] = os.getenv("LANGSMITH_API_KEY", "***************************************************")
os.environ["LANGCHAIN_PROJECT"] = os.getenv("LANGSMITH_PROJECT", "pr-pertinent-pegboard-12")


class ScannerAgent(BaseAgent):
    """
    Codebase security and quality analysis agent.
    
    This agent performs comprehensive analysis including:
    - Security vulnerability detection
    - Code quality assessment
    - Test coverage analysis
    - Dependency security checks
    - Performance issue identification
    - GitHub issue creation for findings
    """
    
    def __init__(
        self, 
        *,
        model_provider: Optional[Literal["anthropic", "openai"]] = "anthropic",
        model_name: Optional[str] = "claude-sonnet-4-20250514",
        use_sandbox: bool = True,
    ):
        """
        Initialize ScannerAgent with LLM configuration.
        
        Args:
            model_provider: Model provider ("anthropic" or "openai")
            model_name: Specific model name
            use_sandbox: Whether to use sandbox for code execution
        """
        super().__init__(
            model_provider=model_provider, 
            model_name=model_name, 
            use_sandbox=use_sandbox
        )

        # Include both sandbox tools and GitHub tools for scan agent
        base_tools = read_tools if use_sandbox else local_tools
        self.tools = base_tools + github_tools
        self.graph = ScannerGraph(llm=self.llm, tools=self.tools).compile()

    
    async def _execute(self, query: str, config: Optional[RunnableConfig], scan_type: Optional[ScanType] = None) -> str:
        """
        Execute the ScannerAgent logic using the scanner graph.
        
        Args:
            query: The scanning task or analysis request
            config: Optional configuration for the agent
            scan_type: Optional specific metric to scan, defaults to comprehensive scan
            
        Returns:
            The final analysis report from the assistant
        """
        
        # Stream and pretty print
        last_chunk = None
        async for chunk in self.graph.astream({
            "query": query,
            "scan_type": scan_type
        }, config=config):
            # Pretty print the messages as they come
            if "reasoner" in chunk:
                for msg in chunk["reasoner"].get("messages", []):
                    if isinstance(msg, (AIMessage, HumanMessage)) and msg.content:
                        print(msg.pretty_print())
                      
            elif "tools" in chunk:
                for msg in chunk["tools"].get("messages", []):
                    if isinstance(msg, ToolMessage):
                        print(msg.pretty_print())
            
            last_chunk = chunk
        
        if last_chunk is None:
            raise ValueError("No response was generated from the agent")
        
        # Extract the final AI message from the state
        messages = last_chunk["reasoner"].get("messages", [])
        return messages[-1].content


# Example usage
if __name__ == "__main__":
    async def main():
        # Example 1: Initialize scanner agent
        scan_agent = ScannerAgent(
            model_provider="anthropic", 
            model_name="claude-sonnet-4-20250514"
        )
        
        # Example 2: Comprehensive scan (all 7 metrics)
        response = await scan_agent.run("Perform a comprehensive codebase analysis")
        print(f"\nFinal Analysis: {response}")
        
        # Example 3: Targeted security scan
        # Note: To use specific scan types, you would need to modify the run method
        # or create a custom method that accepts scan_type parameter
        
        # Example of how it could work with modified interface:
        # response = await scan_agent.run_with_type(
        #     "Scan for security vulnerabilities", 
        #     scan_type=ScanType.SECURITY
        # )
        
        # Example 4: Test coverage specific scan
        # response = await scan_agent.run_with_type(
        #     "Analyze test coverage and identify gaps",
        #     scan_type=ScanType.TEST_COVERAGE
        # )
    
    # Run the async main function
    asyncio.run(main())
