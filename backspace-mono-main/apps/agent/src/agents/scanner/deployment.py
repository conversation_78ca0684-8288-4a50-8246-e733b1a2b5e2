import os
from agents.scanner.agent import ScannerAgent

# Configure <PERSON><PERSON><PERSON> environment variables
os.environ["LANGCHAIN_TRACING_V2"] = "true"
os.environ["LANGCHAIN_ENDPOINT"] = "https://api.smith.langchain.com"
os.environ["LANGCHAIN_API_KEY"] = os.getenv("LANGSMITH_API_KEY", "***************************************************")
os.environ["LANGCHAIN_PROJECT"] = os.getenv("LANGSMITH_PROJECT", "pr-pertinent-pegboard-12")

# Get the compiled graph for deployment
agent = ScannerAgent().graph  # returns CompiledGraph

# Example usage with scan types:
# 
# For targeted security scan:
# async for chunk in agent.astream({
#     "query": "Scan for security vulnerabilities",
#     "scan_type": ScanType.SECURITY
# }):
#     print(chunk)
#
# For comprehensive scan (all 7 metrics):
# async for chunk in agent.astream({
#     "query": "Perform comprehensive analysis",
#     "scan_type": ScanType.COMPREHENSIVE  # or None
# }):
#     print(chunk)
#
# For test coverage specific scan:
# async for chunk in agent.astream({
#     "query": "Analyze test coverage",
#     "scan_type": ScanType.TEST_COVERAGE
# }):
#     print(chunk)