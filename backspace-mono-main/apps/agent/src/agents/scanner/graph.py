from agents.scanner.states import GraphState, ScanType
from langchain_core.messages import HumanMessage
from typing import Any
from langchain_core.language_models.chat_models import BaseChatModel
from langgraph.graph.state import CompiledGraph, StateGraph
from langgraph.prebuilt import ToolNode
from langgraph.graph import END, START
from langchain_core.runnables.config import RunnableConfig
from supabase import create_client, Client

from utils.sandbox_manager import sandbox_manager
from db import db_manager

import logging
import json
from datetime import datetime
import os

# LangSmith imports - using the same pattern as Coder Agent
from langsmith import traceable, get_current_run_tree

logger = logging.getLogger(__name__)

from agents.scanner.prompts import (
    REASONER_SYSTEM_MESSAGE,
    USER_PROMPT_TEMPLATE,
    get_metric_prompt,
    get_metric_user_prompt,
    METRIC_CONFIGS
)

from dotenv import load_dotenv
from langchain_core.tools import BaseTool

from agents.base import BaseGraph

load_dotenv()

class ScannerGraph(BaseGraph):
    def __init__(self, llm: BaseChatModel, tools: list[BaseTool]):
        super().__init__(llm=llm, tools=tools)

    # =================================== NODES ====================================

    @traceable(
        name="Scanner Start Sandbox",
        run_type="chain",
        metadata={"agent_type": "scanner", "phase": "initialization"},
        tags=["sandbox", "scanner-agent", "initialization"]
    )
    async def start_sandbox(self, state: GraphState, config: RunnableConfig) -> dict[str, Any]:
        """Initialize and start a Daytona sandbox for the session.

        Args:
            state: Current graph state
            config: Runtime configuration for the graph

        Returns:
            Updated state with sandbox session_id
        """
        # Get current trace for dynamic updates
        current_run = get_current_run_tree()

        logger.info("🚀 Starting sandbox for deep scan agent session...")

        # Log what we're processing
        repo_id = state.get("repo_id") or "990171565"
        if current_run:
            current_run.add_metadata({
                "repo_id": repo_id,
                "scan_type": str(state.get("scan_type", "comprehensive")),
                "query_preview": state.get("query", "")[:100] if state.get("query") else None
            })

        # Ensure database connection is established
        try:
            await db_manager.connect()
            logger.info("✅ Database connection established")
            if current_run:
                current_run.add_metadata({"database_connected": True})
        except Exception as e:
            logger.error(f"❌ Failed to connect to database: {e}")
            if current_run:
                current_run.add_metadata({"database_connected": False, "db_error": str(e)})
            # Continue anyway - some tools might still work without database

        try:
            logger.info(f"Using repo_id: {repo_id}")

            session_id, sandbox = await sandbox_manager.create_session(repo_id=repo_id)
            logger.info(f"✅ Sandbox started successfully with session {session_id}")

            if current_run:
                current_run.add_metadata({
                    "sandbox_session_id": session_id,
                    "sandbox_created": True
                })

            return {
                "sandbox_session_id": session_id,
            }
        except Exception as e:
            logger.error(f"❌ Failed to start sandbox: {e}")
            if current_run:
                current_run.add_metadata({
                    "sandbox_created": False,
                    "sandbox_error": str(e)
                })
            return {
                "sandbox_session_id": None
            }


    @traceable(
        name="Scanner Close Sandbox",
        run_type="chain",
        metadata={"agent_type": "scanner", "phase": "cleanup"},
        tags=["sandbox", "scanner-agent", "cleanup"]
    )
    async def close_sandbox(self, state: GraphState, config: RunnableConfig) -> dict[str, Any]:
        """Clean up and close the Daytona sandbox.

        Args:
            state: Current graph state containing sandbox session ID
            config: Runtime configuration for the graph

        Returns:
            Updated state with sandbox cleanup status
        """
        # Get current trace for dynamic updates
        current_run = get_current_run_tree()

        session_id = state.get("sandbox_session_id")

        # Log what we're cleaning up
        if current_run:
            current_run.add_metadata({
                "session_id": session_id,
                "has_session": bool(session_id)
            })

        try:
            if session_id:
                success = await sandbox_manager.cleanup_session(session_id)
                if success:
                    logger.info("✅ Sandbox cleaned up successfully")
                    if current_run:
                        current_run.add_metadata({"cleanup_success": True})
                else:
                    logger.warning("⚠️ Sandbox cleanup had issues")
                    if current_run:
                        current_run.add_metadata({"cleanup_success": False, "cleanup_warning": "Had issues"})
            else:
                if current_run:
                    current_run.add_metadata({"cleanup_success": True, "cleanup_reason": "No session to clean"})

            return {
                "sandbox_session_id": None,
            }
        except Exception as e:
            logger.error(f"⚠️ Error during sandbox cleanup: {e}")
            if current_run:
                current_run.add_metadata({
                    "cleanup_success": False,
                    "cleanup_error": str(e)
                })
            return {
                "sandbox_session_id": None,
            }

    # Reasoner node
    @traceable(
        name="Scanner Reasoner",
        run_type="llm",
        metadata={"agent_type": "scanner", "phase": "reasoning"},
        tags=["reasoning", "scanner-agent"]
    )
    async def reasoner(self, state: GraphState) -> dict[str, Any]:
        # Get current trace for dynamic updates
        current_run = get_current_run_tree()

        messages = state["messages"]
        query = state["query"]
        scan_type = state.get("scan_type")
        is_first_message = not messages and query

        # Log what we're processing
        if current_run:
            current_run.add_metadata({
                "message_count": len(messages),
                "is_first_message": is_first_message,
                "query_preview": query[:100] if query else None,
                "scan_type": str(scan_type) if scan_type else "comprehensive"
            })

        # Select the appropriate system and user prompts based on scan_type
        # Handle both string and enum inputs for comparison
        is_comprehensive = (
            scan_type == ScanType.COMPREHENSIVE or
            scan_type == "comprehensive" or
            scan_type is None
        )

        if scan_type and not is_comprehensive:
            # Use metric-specific prompts for targeted scan
            # Handle both string and enum inputs
            if isinstance(scan_type, str):
                metric_key = scan_type
            else:
                metric_key = scan_type.value

            if metric_key in METRIC_CONFIGS:
                system_message = get_metric_prompt(metric_key)
                logger.info(f"🎯 Using {metric_key} specific prompt for targeted scan")

                # Use metric-specific user prompt
                if is_first_message:
                    formatted_query = get_metric_user_prompt(metric_key, str(query))
                    human_message = HumanMessage(content=formatted_query)
                    messages = [human_message]
            else:
                # Fallback to comprehensive if invalid metric
                system_message = REASONER_SYSTEM_MESSAGE
                logger.warning(f"⚠️ Invalid scan_type {scan_type}, using comprehensive prompt")

                if is_first_message:
                    formatted_query = USER_PROMPT_TEMPLATE.replace("{query}", str(query))
                    human_message = HumanMessage(content=formatted_query)
                    messages = [human_message]
        else:
            # Use comprehensive prompt for full scan
            system_message = REASONER_SYSTEM_MESSAGE
            logger.info("📊 Using comprehensive prompt for full 7-metric scan")

            if is_first_message:
                formatted_query = USER_PROMPT_TEMPLATE.replace("{query}", str(query))
                human_message = HumanMessage(content=formatted_query)
                messages = [human_message]

        messages = [{"role": "system", "content": system_message}] + messages

        # Log the prompt being used
        if current_run:
            current_run.add_metadata({
                "prompt_type": "metric_specific" if scan_type and not is_comprehensive else "comprehensive",
                "system_message_length": len(system_message),
                "total_messages": len(messages)
            })

        # This LLM call will now be fully traced
        result = await self.llm.ainvoke(messages)

        # Log the decision made
        if current_run:
            current_run.add_metadata({
                "has_tool_calls": bool(result.tool_calls) if hasattr(result, 'tool_calls') else False,
                "response_length": len(str(result)),
                "tool_call_count": len(result.tool_calls) if hasattr(result, 'tool_calls') and result.tool_calls else 0
            })

        if is_first_message:
            return {"messages": [human_message, result]}
        else:
            return {"messages": [result]}
        

    # =================================== TOOLS NODE ====================================

    @traceable(
        name="Scanner Tools",
        run_type="tool",
        metadata={"agent_type": "scanner", "phase": "tool_execution"},
        tags=["tools", "scanner-agent"]
    )
    async def tools_node(self, state: GraphState) -> dict[str, Any]:
        """Custom tools node with LangSmith tracing."""
        # Get current trace for dynamic updates
        current_run = get_current_run_tree()

        messages = state["messages"]
        last_message = messages[-1]

        # Log tool calls details
        tool_calls = getattr(last_message, 'tool_calls', [])
        if current_run:
            current_run.add_metadata({
                "tool_count": len(tool_calls),
                "tool_names": [tc.get("name", "unknown") for tc in tool_calls],
                "has_tool_calls": bool(tool_calls)
            })

        # Use the standard ToolNode functionality
        tool_node = ToolNode(self.tools)
        result = await tool_node.ainvoke(state)

        # Log the results
        if current_run and result.get("messages"):
            tool_results = []
            for msg in result["messages"]:
                if hasattr(msg, 'name') and hasattr(msg, 'content'):
                    tool_results.append({
                        "tool_name": msg.name,
                        "result_length": len(str(msg.content)),
                        "success": "error" not in str(msg.content).lower()
                    })
            current_run.add_metadata({"tool_results": tool_results})

        return result

    # =================================== EDGE CONDITIONS ====================================

    def should_continue(self, state):
        messages = state["messages"]
        last_message = messages[-1]
        # If there are no tool calls, then we close the sandbox
        if not last_message.tool_calls:
            return "close_sandbox"
        # Otherwise if there is, we continue with tools
        else:
            return "continue"

    def compile(self) -> CompiledGraph:
        builder = StateGraph(GraphState)

        # Add all nodes with sandbox lifecycle management
        builder.add_node("start_sandbox", self.start_sandbox, retry=self.retry_policy)
        builder.add_node("reasoner", self.reasoner, retry=self.retry_policy)
        builder.add_node("tools", self.tools_node, retry=self.retry_policy)  # Use our custom tools node
        builder.add_node("close_sandbox", self.close_sandbox, retry=self.retry_policy)

        # Define the flow: START -> start_sandbox -> reasoner -> tools/close_sandbox -> END
        builder.add_edge(START, "start_sandbox")
        builder.add_edge("start_sandbox", "reasoner")
        builder.add_edge("tools", "reasoner")
        builder.add_edge("close_sandbox", END)

        # Conditional edges from reasoner
        builder.add_conditional_edges(
            "reasoner",
            self.should_continue,
            {
                "continue": "tools",
                "close_sandbox": "close_sandbox",
            }
        )

        return builder.compile()
    
