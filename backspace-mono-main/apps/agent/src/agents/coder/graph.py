from agents.base import BaseGraph
from agents.coder.states import GraphState
from langchain_core.messages import HumanMessage
from typing import Any
from langchain_core.language_models.chat_models import BaseChatModel
from langgraph.graph.state import CompiledGraph, StateGraph
from langgraph.prebuilt import ToolNode
from langgraph.graph import END, START
from langchain_core.runnables.config import RunnableConfig

from utils.sandbox_manager import sandbox_manager
from db import db_manager

from agents.coder.prompts import REASONER_SYSTEM_MESSAGE, USER_PROMPT_TEMPLATE

from dotenv import load_dotenv
from langchain_core.tools import BaseTool

import os

import logging

# Add LangSmith observability imports
from langsmith import traceable, get_current_run_tree

logger = logging.getLogger(__name__)

load_dotenv()



class CoderGraph(BaseGraph):
    def __init__(self, llm: BaseChatModel, tools: list[BaseTool], use_sandbox: bool = True):
        super().__init__(llm=llm, tools=tools)
        self.use_sandbox = use_sandbox

    # =================================== NODES ====================================

    async def start_sandbox(self, state: GraphState, config: RunnableConfig) -> dict[str, Any]:
        """Initialize and start a Daytona sandbox for the session.
        
        Args:
            state: Current graph state
            config: Runtime configuration for the graph
            
        Returns:
            Updated state with sandbox session_id
        """
        logger.info("🚀 Starting sandbox for coder agent session...")
        
        # Ensure database connection is established
        try:
            await db_manager.connect()
            logger.info("✅ Database connection established")
        except Exception as e:
            logger.error(f"❌ Failed to connect to database: {e}")
            # Continue anyway - some tools might still work without database
        
        try:
            # Get repo_id from state, with fallback to environment variable
            repo_id = state.get("repo_id") or os.environ.get("DEFAULT_REPO_ID")
            logger.info(f"Using repo_id: {repo_id}")
            
            session_id, sandbox = await sandbox_manager.create_session(repo_id=repo_id)
            logger.info(f"✅ Sandbox started successfully with session {session_id}")
            
            return {
                "sandbox_session_id": session_id,
            }
        except Exception as e:
            logger.error(f"❌ Failed to start sandbox: {e}")
            return {
                "sandbox_session_id": None
            }

    async def close_sandbox(self, state: GraphState, config: RunnableConfig) -> dict[str, Any]:
        """Clean up and close the Daytona sandbox.
        
        Args:
            state: Current graph state containing sandbox session ID
            config: Runtime configuration for the graph
            
        Returns:
            Updated state with sandbox cleanup status
        """
        session_id = state.get("sandbox_session_id")
        
        try:
            if session_id:
                success = await sandbox_manager.cleanup_session(session_id)
                if success:
                    logger.info("✅ Sandbox cleaned up successfully")
                else:
                    logger.warning("⚠️ Sandbox cleanup had issues")
            
            return {
                "sandbox_session_id": None,
            }
        except Exception as e:
            logger.error(f"⚠️ Error during sandbox cleanup: {e}")
            return {
                "sandbox_session_id": None,
            }

    # Reasoner node
    @traceable(
        name="Coder Reasoner",
        run_type="llm",
        metadata={"agent_type": "coder", "phase": "reasoning"},
        tags=["reasoning", "coder-agent"]
    )
    async def reasoner(self, state: GraphState) -> dict[str, Any]:
        # Get current trace for dynamic updates
        current_run = get_current_run_tree()

        messages = state["messages"]
        query = state["query"]
        is_first_message = not messages and query

        # Log what we're processing
        if current_run:
            current_run.add_metadata({
                "message_count": len(messages),
                "is_first_message": is_first_message,
                "query_preview": query[:100] if query else None
            })

        if is_first_message:
            formatted_query = USER_PROMPT_TEMPLATE.format(query=query)
            human_message = HumanMessage(content=formatted_query)
            messages = [human_message]

        messages = [{"role": "system", "content": REASONER_SYSTEM_MESSAGE}] + messages

        # This LLM call will now be fully traced
        result = await self.llm.ainvoke(messages)

        # Log the decision made
        if current_run:
            current_run.add_metadata({
                "has_tool_calls": bool(result.tool_calls) if hasattr(result, 'tool_calls') else False,
                "response_length": len(str(result))
            })

        if is_first_message:
            return {"messages": [human_message, result]}
        else:
            return {"messages": [result]}

    @traceable(
        name="Coder Tools",
        run_type="tool",
        metadata={"agent_type": "coder", "phase": "tool_execution"},
        tags=["tools", "coder-agent", "execution"]
    )
    async def tools_node(self, state: GraphState) -> dict[str, Any]:
        """Custom tools node with LangSmith tracing."""
        # Get current trace for dynamic updates
        current_run = get_current_run_tree()

        messages = state["messages"]
        last_message = messages[-1]

        # Log tool calls details
        tool_calls = getattr(last_message, 'tool_calls', [])
        if current_run:
            current_run.add_metadata({
                "tool_count": len(tool_calls),
                "tool_names": [tc.get("name", "unknown") for tc in tool_calls],
                "has_tool_calls": bool(tool_calls)
            })

        # Use the standard ToolNode functionality
        tool_node = ToolNode(self.tools)
        result = await tool_node.ainvoke(state)

        # Log the results
        if current_run and result.get("messages"):
            tool_results = []
            for msg in result["messages"]:
                if hasattr(msg, 'name') and hasattr(msg, 'content'):
                    tool_results.append({
                        "tool_name": msg.name,
                        "result_length": len(str(msg.content)),
                        "success": "error" not in str(msg.content).lower()
                    })
            current_run.add_metadata({"tool_results": tool_results})

        return result


    # =================================== EDGE CONDITIONS ====================================

    @traceable(
        name="Should Continue Decision",
        run_type="chain",
        tags=["decision", "routing"]
    )
    def should_continue(self, state):
        current_run = get_current_run_tree()

        messages = state["messages"]
        last_message = messages[-1]

        # Log decision context
        decision_context = {
            "last_message_type": type(last_message).__name__,
            "has_tool_calls": bool(getattr(last_message, 'tool_calls', None)),
            "use_sandbox": self.use_sandbox,
            "message_count": len(messages)
        }

        if current_run:
            current_run.add_metadata(decision_context)

        # Make decision with logging
        if not last_message.tool_calls:
            decision = "close_sandbox" if self.use_sandbox else "end"
            reason = "No tool calls detected"
        else:
            decision = "continue"
            reason = "Tool calls found, continuing execution"

        # Log the decision and reasoning
        if current_run:
            current_run.add_metadata({
                "decision": decision,
                "reason": reason
            })

        return decision

    def compile(self) -> CompiledGraph:
        builder = StateGraph(GraphState)

        if self.use_sandbox:
            # Add nodes with sandbox lifecycle management
            builder.add_node("start_sandbox", self.start_sandbox, retry=self.retry_policy)
            builder.add_node("reasoner", self.reasoner, retry=self.retry_policy)
            builder.add_node("tools", self.tools_node, retry=self.retry_policy)
            builder.add_node("close_sandbox", self.close_sandbox, retry=self.retry_policy)

            # Define the flow: START -> start_sandbox -> reasoner -> tools/close_sandbox -> END
            builder.add_edge(START, "start_sandbox")
            builder.add_edge("start_sandbox", "reasoner")
            builder.add_edge("tools", "reasoner")
            builder.add_edge("close_sandbox", END)

            # Conditional edges from reasoner
            builder.add_conditional_edges(
                "reasoner",
                self.should_continue,
                {
                    "continue": "tools",
                    "close_sandbox": "close_sandbox",
                }
            )
        else:
            # Local graph without sandbox nodes
            builder.add_node("reasoner", self.reasoner, retry=self.retry_policy)
            builder.add_node("tools", self.tools_node, retry=self.retry_policy)

            # Define the flow: START -> reasoner -> tools/END
            builder.add_edge(START, "reasoner")
            builder.add_edge("tools", "reasoner")

            # Conditional edges from reasoner
            builder.add_conditional_edges(
                "reasoner",
                self.should_continue,
                {
                    "continue": "tools",
                    "end": END,
                }
            )

        return builder.compile()

