#!/usr/bin/env python3
"""
Test script to verify Scanner Agent with <PERSON><PERSON><PERSON> observability.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from agents.scanner.deployment import agent
from agents.scanner.states import ScanType

async def test_scanner_with_langsmith():
    """Test the Scanner Agent with LangSmith tracing."""
    
    print("🔍 Testing Scanner Agent with LangSmith observability...")
    print(f"📊 LangSmith Project: {os.getenv('LANGSMITH_PROJECT', 'pr-pertinent-pegboard-12')}")
    print(f"🔑 LangSmith API Key: {os.getenv('LANGSMITH_API_KEY', 'Not set')[:20]}...")
    
    try:
        # Test with a simple security scan
        print("\n🛡️ Running security scan...")
        
        initial_state = {
            "query": "create a simple test issue",
            "scan_type": ScanType.SECURITY,
            "repo_id": "990171565",  # backspace-monorepo
            "messages": []
        }
        
        # Stream the results
        async for chunk in agent.astream(initial_state):
            print(f"📦 Chunk: {chunk}")
            
        print("\n✅ Scanner test completed!")
        print("🔗 Check your LangSmith dashboard for traces: https://smith.langchain.com/")
        
    except Exception as e:
        print(f"❌ Error during scanner test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_scanner_with_langsmith())
