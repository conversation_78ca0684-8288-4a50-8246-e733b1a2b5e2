#!/usr/bin/env python3
"""
Test script to verify <PERSON><PERSON><PERSON> observability implementation.
This tests the black box fixes we implemented.
"""

import asyncio
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up environment variables for LangSmith
os.environ["LANGSMITH_TRACING"] = "true"
os.environ["LANGSMITH_API_KEY"] = "***************************************************"
os.environ["LANGSMITH_PROJECT"] = "pr-flowery-gastropod-81"

# Simple test using just <PERSON><PERSON><PERSON> decorators
from langsmith import traceable, get_current_run_tree
from langchain_anthropic import ChatAnthropic
from langchain_core.messages import HumanMessage

@traceable(
    name="Test Reasoner Simulation",
    run_type="llm",
    metadata={"test_type": "observability_demo"},
    tags=["test", "observability"]
)
async def test_reasoner_simulation(query: str):
    """Simulate the reasoner method to test observability."""
    current_run = get_current_run_tree()

    print(f"🧪 Testing LangSmith Observability with query: {query}")

    # Log what we're processing (like our real reasoner)
    if current_run:
        current_run.add_metadata({
            "query_length": len(query),
            "query_preview": query[:100],
            "simulation": True
        })

    # Simulate LLM call
    await asyncio.sleep(1)  # Simulate processing time

    # Simulate decision making
    has_tool_calls = "function" in query.lower() or "create" in query.lower()

    if current_run:
        current_run.add_metadata({
            "has_tool_calls": has_tool_calls,
            "decision_reason": "Detected function creation request" if has_tool_calls else "No tool calls needed"
        })

    return {
        "response": f"I'll help you {query}",
        "needs_tools": has_tool_calls
    }

@traceable(
    name="Test Decision Logic",
    run_type="chain",
    tags=["decision", "test"]
)
def test_decision_logic(result: dict):
    """Simulate the should_continue method."""
    current_run = get_current_run_tree()

    # Log decision context
    if current_run:
        current_run.add_metadata({
            "needs_tools": result["needs_tools"],
            "response_length": len(result["response"])
        })

    decision = "continue" if result["needs_tools"] else "end"
    reason = "Tools needed for function creation" if result["needs_tools"] else "No tools required"

    if current_run:
        current_run.add_metadata({
            "decision": decision,
            "reason": reason
        })

    return decision

async def test_observability():
    """Test LangSmith observability implementation."""
    print("🧪 Testing LangSmith Observability Implementation...")

    # Test 1: Reasoner simulation
    print("\n📝 Testing reasoner observability...")
    result = await test_reasoner_simulation("Create a simple Python function that adds two numbers")
    print(f"✅ Reasoner result: {result}")

    # Test 2: Decision logic simulation
    print("\n🔀 Testing decision logic observability...")
    decision = test_decision_logic(result)
    print(f"✅ Decision: {decision}")

    print("\n🎯 Test completed! Check your LangSmith dashboard:")
    print("   https://smith.langchain.com/")
    print("   Project: pr-flowery-gastropod-81")
    print("\n📊 You should now see:")
    print("   ✅ 'Test Reasoner Simulation' trace with detailed metadata")
    print("   ✅ 'Test Decision Logic' trace with decision reasoning")
    print("   ✅ All the internal thinking that was previously a black box!")

    # Flush traces to ensure they're sent
    try:
        from langsmith import Client
        client = Client()
        if hasattr(client, 'flush') and callable(client.flush):
            await client.flush()
            print("\n✅ Traces flushed to LangSmith")
        else:
            print("\n✅ Traces sent to LangSmith (flush not available)")
    except Exception as e:
        print(f"\n⚠️  Trace flush failed (traces may still be sent): {e}")

if __name__ == "__main__":
    asyncio.run(test_observability())
