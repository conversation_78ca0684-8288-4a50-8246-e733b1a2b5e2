{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import logging\n", "logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'agents'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[2], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01magents\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01<PERSON><PERSON><PERSON><PERSON>_coder\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdeployment\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m agent, graph\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mdb\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m db_manager\n", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'agents'"]}], "source": ["from agents.claude_coder.deployment import agent, graph\n", "import asyncio\n", "from db import db_manager\n", "\n", "# Connect to database\n", "await db_manager.connect()\n", "\n", "import asyncio\n", "import logging\n", "\n", "# Enable logging to see what's happening\n", "logging.basicConfig(level=logging.INFO)\n", "\n", "\n", "\n", "from agents.claude_coder.states import ClaudeCoderState\n", "\n", "# Your custom configuration\n", "initial_state: ClaudeCoderState = {\n", "    \"sandbox\": None,\n", "    \"branch_name\": \"tawsif/hiring\",  # Your branch\n", "    \"base_branch\": \"main\",\n", "    \"repo_path\": \"/Users/<USER>/Documents/projects/backspace/deep-research-code\",\n", "    \"current_phase\": \"\",\n", "    \"phase_results\": {},\n", "    \"pr_url\": None,\n", "    \"error\": None,\n", "    \"claude_options\": {\n", "        \"max-turns\": \"100\"\n", "    }\n", "}\n", "\n", "print(\"🎯 Running with your specific branch configuration...\")\n", "result = await graph.ainvoke(initial_state)\n", "print(f\"Result: {result}\")\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:agents.claude_e2b.e2b_sandbox:🚀 Creating E2B sandbox with template: vcomjjr43nxwhfxodbqm\n", "INFO:agents.claude_e2b.e2b_sandbox:🔑 Generating fresh GitHub token for repo 990171565\n", "INFO:httpx:HTTP Request: GET https://tnkgfginqwcrtplppxfz.supabase.co/rest/v1/repositories?select=id%2Cintegration_id%2Curl%2Cname&id=eq.990171565 \"HTTP/2 200 OK\"\n", "INFO:httpx:HTTP Request: GET https://tnkgfginqwcrtplppxfz.supabase.co/rest/v1/integrations?select=data&id=eq.14 \"HTTP/2 200 OK\"\n", "INFO:utils.github_auth:Generating installation token for installation 71548156\n", "INFO:utils.github_auth:✅ Generated token for installation 71548156\n", "INFO:agents.claude_e2b.e2b_sandbox:📦 Initializing E2B sandbox (timeout: 300s)...\n", "INFO:e2b.api:Request POST https://api.e2b.app/sandboxes\n", "INFO:httpx:HTTP Request: POST https://api.e2b.app/sandboxes \"HTTP/1.1 201 Created\"\n", "INFO:e2b.api:Response 201\n", "INFO:agents.claude_e2b.e2b_sandbox:✅ Successfully created E2B sandbox with ID: itjtig84i67t6tw8nd2ve-024b37b9\n", "INFO:agents.claude_e2b.e2b_sandbox:🔧 Setting up workspace and tools...\n", "INFO:agents.claude_e2b.e2b_sandbox:📍 Checking current directory...\n", "INFO:agents.claude_e2b.e2b_sandbox:👤 Checking user...\n", "INFO:agents.claude_e2b.e2b_sandbox:📁 Checking workspace path...\n", "INFO:agents.claude_e2b.e2b_sandbox:📂 Creating workspace directory...\n", "INFO:agents.claude_e2b.e2b_sandbox:📍 Verifying workspace...\n", "INFO:agents.claude_e2b.e2b_sandbox:🔍 Checking Claude Code installation...\n", "INFO:agents.claude_e2b.e2b_sandbox:📦 Ensuring Claude Code is installed...\n", "INFO:agents.claude_e2b.e2b_sandbox:🔧 Configuring git...\n", "INFO:agents.claude_e2b.e2b_sandbox:🤖 Verifying Claude Code installation...\n", "INFO:agents.claude_e2b.e2b_sandbox:   ✅ Claude Code version: 1.0.30 (<PERSON>)\n", "INFO:agents.claude_e2b.e2b_sandbox:📂 Cloning repository into workspace...\n", "INFO:httpx:HTTP Request: GET https://tnkgfginqwcrtplppxfz.supabase.co/rest/v1/repositories?select=url&id=eq.990171565 \"HTTP/2 200 OK\"\n", "INFO:agents.claude_e2b.e2b_sandbox:🔗 Repository URL: https://github.com/backspace-org/backspace-mono\n", "INFO:agents.claude_e2b.e2b_sandbox:🔄 Cloning repository...\n", "INFO:agents.claude_e2b.e2b_sandbox:✅ Repository cloned successfully\n", "INFO:agents.claude_e2b.e2b_sandbox:📋 Workspace contents:\n", "total 632\n", "drwxr-xr-x 6 <USER> <GROUP>   4096 Jun 23 22:34 .\n", "drwx------ 5 <USER> <GROUP>   4096 Jun 23 22:34 ..\n", "-rw-r--r-- 1 <USER> <GROUP>    260 Jun 23 22:34 .eslintrc.js\n", "drwxr-xr-x 8 <USER> <GROUP>   4096 Jun 23 22:34 .git\n", "-rw-r--r-- 1 <USER> <GROUP>    685 Jun 23 22:34 .giti<PERSON>re\n", "-rw-r--r-- 1 <USER> <GROUP>      0 Jun 23 22:34 .npmrc\n", "drwxr-xr-x 2 <USER> <GROUP>   4096 Jun 23 22:34 .vscode\n", "-rw-r--r-- 1 <USER> <GROUP>   2828 Jun 23 22:34 README.md\n", "drwxr-xr-x 7 <USER> <GROUP>   4096 Jun 23 22:34 apps\n", "drwxr-xr-x 5 <USER> <GROUP>   4096 Jun 23 22:34 backspace-cli\n", "-rw-r--r-- 1 <USER> <GROUP>    346 Jun 23 22:34 package.json\n", "-rw-r--r-- 1 <USER> <GROUP> 491128 Jun 23 22:34 pnpm-lock.yaml\n", "-rw-r--r-- 1 <USER> <GROUP>     40 Jun 23 22:34 pnpm-workspace.yaml\n", "-rw-r--r-- 1 <USER> <GROUP> 100279 Jun 23 22:34 test.ipynb\n", "-rw-r--r-- 1 <USER> <GROUP>   2541 Jun 23 22:34 test_docker_builder.py\n", "-rw-r--r-- 1 <USER> <GROUP>    367 Jun 23 22:34 turbo.json\n", "\n", "INFO:agents.claude_e2b.e2b_sandbox:🔍 Git check: Git repository found\n", "INFO:agents.claude_e2b.e2b_sandbox:🎉 Sandbox setup complete!\n"]}], "source": ["# Cell 1: Test run_claude_in_sandbox (waits for completion, returns full session)\n", "import asyncio\n", "from agents.claude_e2b import create_sandbox, cleanup_sandbox, run_claude_in_sandbox\n", "from db import db_manager\n", "\n", "\n", "# Connect to database\n", "await db_manager.connect()\n", "\n", "# Create sandbox\n", "sandbox = await create_sandbox()"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"ename": "CommandExitException", "evalue": "Command exited with code 128 and error:\nfatal: bad sha1 reference roland/hooks\n", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mCommandExitException\u001b[39m                      Traceback (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[17]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m result = \u001b[38;5;28;01mawait\u001b[39;00m sandbox.commands.run(\u001b[33m\"\u001b[39m\u001b[33mcd workspace && git show-branch main roland/hooks\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m      2\u001b[39m \u001b[38;5;28mprint\u001b[39m(result.stdout)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Documents/projects/backspace/deep-research-code/apps/agent/.venv/lib/python3.12/site-packages/e2b/sandbox_async/commands/command.py:215\u001b[39m, in \u001b[36mCommands.run\u001b[39m\u001b[34m(self, cmd, background, envs, user, cwd, on_stdout, on_stderr, timeout, request_timeout)\u001b[39m\n\u001b[32m    192\u001b[39m \u001b[38;5;28;01masync\u001b[39;00m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mrun\u001b[39m(\n\u001b[32m    193\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m    194\u001b[39m     cmd: \u001b[38;5;28mstr\u001b[39m,\n\u001b[32m   (...)\u001b[39m\u001b[32m    202\u001b[39m     request_timeout: Optional[\u001b[38;5;28mfloat\u001b[39m] = \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[32m    203\u001b[39m ):\n\u001b[32m    204\u001b[39m     proc = \u001b[38;5;28;01mawait\u001b[39;00m \u001b[38;5;28mself\u001b[39m._start(\n\u001b[32m    205\u001b[39m         cmd,\n\u001b[32m    206\u001b[39m         envs,\n\u001b[32m   (...)\u001b[39m\u001b[32m    212\u001b[39m         on_stderr=on_stderr,\n\u001b[32m    213\u001b[39m     )\n\u001b[32m--> \u001b[39m\u001b[32m215\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m proc \u001b[38;5;28;01mif\u001b[39;00m background \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;01mawait\u001b[39;00m proc.wait()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Documents/projects/backspace/deep-research-code/apps/agent/.venv/lib/python3.12/site-packages/e2b/sandbox_async/commands/command_handle.py:178\u001b[39m, in \u001b[36mAsyncCommandHandle.wait\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    175\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m(\u001b[33m\"\u001b[39m\u001b[33mCommand ended without an end event\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    177\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._result.exit_code != \u001b[32m0\u001b[39m:\n\u001b[32m--> \u001b[39m\u001b[32m178\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m CommandExitException(\n\u001b[32m    179\u001b[39m         stdout=\u001b[38;5;28mself\u001b[39m._stdout,\n\u001b[32m    180\u001b[39m         stderr=\u001b[38;5;28mself\u001b[39m._stderr,\n\u001b[32m    181\u001b[39m         exit_code=\u001b[38;5;28mself\u001b[39m._result.exit_code,\n\u001b[32m    182\u001b[39m         error=\u001b[38;5;28mself\u001b[39m._result.error,\n\u001b[32m    183\u001b[39m     )\n\u001b[32m    185\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m._result\n", "\u001b[31mCommandExitException\u001b[39m: Command exited with code 128 and error:\nfatal: bad sha1 reference roland/hooks\n"]}], "source": ["result = await sandbox.commands.run(\"cd workspace && git show-branch main roland/hooks\")\n", "print(result.stdout)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.0"}}, "nbformat": 4, "nbformat_minor": 4}